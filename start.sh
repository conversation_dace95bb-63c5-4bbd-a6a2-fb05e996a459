#!/bin/bash

# Lobe Midjourney WebUI Docker 启动脚本

echo "🚀 启动 Lobe Midjourney WebUI..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker 未运行，请启动 Docker 服务"
    exit 1
fi

# 构建并启动服务
echo "📦 构建并启动容器..."
docker compose up -d --build

# 检查容器状态
echo "🔍 检查容器状态..."
docker compose ps

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务是否可访问
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 服务启动成功！"
    echo "🌐 访问地址: http://localhost:3000"
    echo ""
    echo "📋 常用命令:"
    echo "  查看日志: docker compose logs -f"
    echo "  停止服务: docker compose down"
    echo "  重启服务: docker compose restart"
else
    echo "❌ 服务启动失败，请检查日志:"
    echo "docker compose logs"
fi
