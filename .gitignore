# Gitignore for LobeHub
################################################################

# general
.DS_Store
.idea
.vscode
.history
.temp
.env.local
venv
temp
tmp

# dependencies
node_modules
*.log
*.lock
package-lock.json

# ci
coverage
.coverage
.eslintcache
.stylelintcache

# production
dist
es
lib
logs
test-output

# umi
.umi
.umi-production
.umi-test
.dumi/tmp*

# husky
.husky/prepare-commit-msg

# misc
# add other ignore file below
.vercel
.next
build
public/dist
docs-dist
bun.lockb
docs/.dumi/*
public/docs
.env
tsconfig.tsbuildinfo
