# Lobe Midjourney WebUI - Docker 部署

## 🎉 部署成功！

恭喜！Lobe Midjourney WebUI 已经成功使用 Docker 部署完成。

## 📋 部署摘要

- **项目**: Lobe Midjourney WebUI
- **技术栈**: Next.js 14, React 18, TypeScript
- **容器化**: Docker + Docker Compose
- **端口**: 3000
- **访问地址**: http://localhost:3000

## 🚀 快速启动

### 一键启动
```bash
./start.sh
```

### 一键停止
```bash
./stop.sh
```

## 📁 项目文件结构

```
├── Dockerfile              # Docker 镜像构建文件
├── docker-compose.yml      # Docker Compose 配置
├── .dockerignore           # Docker 构建忽略文件
├── start.sh                # 一键启动脚本
├── stop.sh                 # 一键停止脚本
├── DOCKER_DEPLOYMENT.md    # 详细部署文档
└── DOCKER_README.md        # 本文件
```

## 🔧 常用命令

### 查看服务状态
```bash
docker compose ps
```

### 查看实时日志
```bash
docker compose logs -f
```

### 重启服务
```bash
docker compose restart
```

### 停止服务
```bash
docker compose down
```

### 重新构建并启动
```bash
docker compose up -d --build
```

## 🌐 访问应用

应用启动后，可以通过以下地址访问：

- **本地访问**: http://localhost:3000
- **局域网访问**: http://[你的IP地址]:3000

## ⚙️ 配置说明

### 环境变量

可以在 `docker-compose.yml` 中修改环境变量：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  # 添加你的 Midjourney 代理 URL
  # - MIDJOURNEY_PROXY_URL=http://your-midjourney-proxy:8080
```

### 端口配置

如需修改端口，编辑 `docker-compose.yml`：

```yaml
ports:
  - "8080:3000"  # 将本地8080端口映射到容器3000端口
```

## 🔗 相关链接

- [项目源码](https://github.com/lobehub/lobe-midjourney-webui)
- [Midjourney Proxy 部署文档](https://github.com/novicezk/midjourney-proxy/)
- [详细部署文档](./DOCKER_DEPLOYMENT.md)

## 📞 技术支持

如果遇到问题，请：

1. 查看 [详细部署文档](./DOCKER_DEPLOYMENT.md)
2. 检查容器日志：`docker compose logs`
3. 访问项目 [GitHub Issues](https://github.com/lobehub/lobe-midjourney-webui/issues)

## 🎯 下一步

1. **配置 Midjourney 代理**: 参考 [Midjourney Proxy 设置指南](./docs/Setup-Midjourney-Proxy.md)
2. **自定义配置**: 根据需要修改环境变量和端口设置
3. **生产部署**: 配置反向代理、SSL证书等

---

**享受使用 Lobe Midjourney WebUI！** 🎨✨
