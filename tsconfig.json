{"compilerOptions": {"module": "CommonJS", "target": "ES5", "lib": ["dom", "dom.iterable", "esnext"], "sourceMap": true, "skipDefaultLibCheck": true, "jsx": "preserve", "baseUrl": ".", "allowSyntheticDefaultImports": true, "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noUnusedLocals": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "paths": {"@/*": ["src/*"]}, "types": ["vitest/globals"], "allowJs": true, "noEmit": true, "incremental": true, "esModuleInterop": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "exclude": ["node_modules"], "include": ["src", "next-env.d.ts", "*.ts", ".next/types/**/*.ts"]}