name: Release CI
on:
  push:
    branches:
      - main

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install bun
        uses: oven-sh/setup-bun@v1

      - name: Install deps
        run: bun i

      - name: CI
        run: bun run ci

      - name: Test
        run: bun run test

      - name: build
        run: bun run build

      - name: release
        run: bun run release
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
