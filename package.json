{"name": "midjourney-webui", "version": "1.5.0", "private": true, "description": "Unleash creativity with Midjou<PERSON>y, effortlessly generating unique AI-driven images through simple prompts.", "homepage": "https://github.com/lobehub/lobe-midjourney-webui", "repository": "https://github.com/lobehub/lobe-midjourney-webui.git", "scripts": {"build": "next build", "ci": "npm run lint && npm run type-check", "dev": "next dev -p 3040", "i18n": "lobe-i18n", "lint": "eslint \"{src,api,docs}/**/*.{js,jsx,ts,tsx}\" --fix", "lint:md": "remark . --quiet --frail --output", "lint:style": "stylelint \"src/**/*.{js,jsx,ts,tsx}\" --fix", "prepare": "husky", "prepublishOnly": "npm run doctor && npm run build", "prettier": "prettier -c --write \"**/**\"", "release": "semantic-release", "start": "next start", "test": "vitest --passWithNoTests", "test:coverage": "vitest --coverage  --passWithNoTests", "type-check": "tsc --noEmit"}, "lint-staged": {"*.md": ["remark --quiet --output --", "prettier --write --no-error-on-unmatched-pattern"], "*.json": ["prettier --write --no-error-on-unmatched-pattern"], "*.{js,jsx}": ["prettier --write", "stylelint --fix", "eslint --fix"], "*.{ts,tsx}": ["prettier --parser=typescript --write", "stylelint --fix", "eslint --fix"]}, "dependencies": {"@lobehub/chat-plugin-sdk": "^1.32.3", "@lobehub/chat-plugins-gateway": "^1.9.0", "@lobehub/i18n-cli": "^1.18.1", "@lobehub/icons": "^1.22.1", "@lobehub/ui": "^1.141.3", "@vercel/analytics": "^1.3.1", "ahooks": "^3.8.0", "antd": "^5.17.4", "antd-style": "^3.6.2", "dayjs": "^1.11.11", "fast-deep-equal": "^3.1.3", "i18next": "^23.11.5", "i18next-browser-languagedetector": "^7.2.1", "i18next-resources-to-backend": "^1.2.1", "immer": "^10.1.1", "lucide-react": "latest", "next": "^14.2.3", "polished": "^4.3.1", "qs": "^6.12.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^14.1.2", "react-layout-kit": "^1.9.0", "rtl-detect": "^1.1.2", "swr": "^2.2.5", "url-join": "^5.0.0", "zustand": "^4.5.2"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@lobehub/lint": "latest", "@next/eslint-plugin-next": "^14.2.3", "@types/qs": "^6.9.15", "@types/react": "^18.3.3", "@types/rtl-detect": "^1.0.3", "@vercel/node": "^3.1.5", "@vitest/coverage-v8": "^1.6.0", "commitlint": "^19.3.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "husky": "^9.0.11", "lint-staged": "^15.2.5", "prettier": "^3.2.5", "remark": "^14.0.3", "remark-cli": "^11.0.0", "semantic-release": "^21.1.2", "stylelint": "^15.11.0", "typescript": "^5.4.5", "vitest": "^1.6.0"}}