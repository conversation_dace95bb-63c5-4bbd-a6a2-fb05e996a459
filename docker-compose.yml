services:
  lobe-midjourney-webui:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      # 添加你的Midjourney代理URL环境变量
      # - MIDJOURNEY_PROXY_URL=http://your-midjourney-proxy:8080
    restart: unless-stopped
    container_name: lobe-midjourney-webui
    
    # 可选：如果你需要持久化数据，可以添加volumes
    # volumes:
    #   - ./data:/app/data

# 可选：如果你想同时部署midjourney-proxy服务
# midjourney-proxy:
#   image: novicezk/midjourney-proxy:latest
#   ports:
#     - "8080:8080"
#   environment:
#     - MJ_DISCORD_GUILD_ID=your_guild_id
#     - MJ_DISCORD_CHANNEL_ID=your_channel_id
#     - MJ_DISCORD_USER_TOKEN=your_user_token
#   restart: unless-stopped
#   container_name: midjourney-proxy
