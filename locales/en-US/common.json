{"appIniting": "App is initializing...", "docs": "Documents", "guide": {"step1": {"description": "Go to settings and fill in the Midjourney API proxy address", "title": "Bind Midjourney API Service"}, "step2": {"description": "Enter the prompt word in the input box and click the generate button to start generating", "title": "Start Mapping"}}, "images": {"delete": "Delete", "deleteAll": "Delete All Images", "deleteAllConfirm": "Are you sure you want to delete all images?", "deleteConfirm": "Are you sure you want to delete this image?", "deleteCurrent": "Delete Current Image"}, "input": {"placeholder": "Enter Midjourney prompt word...", "uploadImage": "Upload Image"}, "requestError": "Request failed, error code {{errorCode}}", "response": {"NO_BASE_URL": "Midjourney API proxy address is empty, please fill it in and try again", "fallback": "Request failed, please try again later"}, "settings": {"MidjourneyAPIProxy": {"apiSecret": {"title": "Midjourney API Proxy Secret"}, "baseUrl": {"title": "Midjourney API Proxy Address"}, "description": "Please refer to <1>midjourney-proxy</1> for deploying the server and then use"}, "modalTitle": "Settings", "save": "Save"}, "task": {"actions": {"download": "Download", "info": "Image Details", "reroll": "<PERSON>roll", "upscale": "Upscale", "variant": "Diversify"}, "starting": "Starting"}}