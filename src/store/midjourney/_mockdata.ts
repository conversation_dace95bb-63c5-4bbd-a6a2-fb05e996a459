import { MidjourneyState } from './initialState';

export const mockState: Partial<MidjourneyState> = {
  activeTaskId: '1705298817774894',
  prompts: 'cute little duckling, soft fluffy feathers, bright eyes, standing by a pond',
  runningTaskIds: [],
  tasks: [
    {
      action: 'IMAGINE',
      description:
        '/imagine cute little duckling, soft fluffy feathers, bright eyes, standing by a pond',
      finishTime: 1705298852605,
      id: '1705298817774894',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196334838370816040/meng1011_cute_little_duckling_soft_fluffy_feathers_bright_eyes__19295cb6-11f9-49c9-a012-5e0bf76946d4.png?ex=65b740a4&is=65a4cba4&hm=ded84a6c8600b73135a9607e0ea4eca14e20eb4a566bf614e1f98450b75585be&',
      progress: '100%',
      prompt: 'cute little duckling, soft fluffy feathers, bright eyes, standing by a pond',
      promptEn: 'cute little duckling, soft fluffy feathers, bright eyes, standing by a pond',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'cute little duckling, soft fluffy feathers, bright eyes, standing by a pond --v 6.0 --s 250',
        flags: 0,
        messageHash: '19295cb6-11f9-49c9-a012-5e0bf76946d4',
        messageId: '1196334838697951244',
        nonce: '1467884005067145216',
        progressMessageId: '1196334697198919881',
      },
      startTime: 1705298817775,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705298817774,
    },
    {
      action: 'IMAGINE',
      description: '/imagine a dog',
      failReason: null,
      finishTime: 1705301559304,
      id: '1705301507336856',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196346191131197490/meng1011_a_dog_a4335ba5-eafa-41b9-baed-a6bb4901e61d.png?ex=65b74b37&is=65a4d637&hm=2e2d695575a05a66b9df93293e0da112e28b6f7a5c9e06ca4bed571b50d65db2&',
      progress: '100%',
      prompt: 'a dog',
      promptEn: 'a dog',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt: 'a dog --v 6.0 --s 250',
        flags: 0,
        messageHash: 'a4335ba5-eafa-41b9-baed-a6bb4901e61d',
        messageId: '1196346191693226096',
        nonce: '1467895285907800064',
        notifyHook: null,
        progressMessageId: '1196345977104253018',
      },
      startTime: 1705301507337,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705301507336,
    },
    {
      action: 'IMAGINE',
      description:
        '/imagine Overhead drone view, speed to the clouds, black and dark green blue tones and a powerful sense of oppression, Portrait of a dragon with two massive wings, hugging body tightly covering the sky, close shot, movie poster vibe, thunderstorm, at night, dynamic photography, huge whirlpool, towering tsunami, photo studio, vibrant vivid color, 135mm Canon lens, Live movie, similar to Pacific Rim --no legs, person, people, man, woman, anime, cg, game --ar 21:9 --v 6',
      failReason: null,
      finishTime: 1705305600408,
      id: '1705305488385054',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196363140699533312/meng1011_Overhead_drone_view_speed_to_the_clouds_black_and_dark_2631fc4d-31d2-4925-9d55-02d010943bdf.png?ex=65b75b00&is=65a4e600&hm=a71f0c85796178895341ae05561e97b2ad5f3a1483100ad2bf294276d1d07065&',
      progress: '100%',
      prompt:
        'Overhead drone view, speed to the clouds, black and dark green blue tones and a powerful sense of oppression, Portrait of a dragon with two massive wings, hugging body tightly covering the sky, close shot, movie poster vibe, thunderstorm, at night, dynamic photography, huge whirlpool, towering tsunami, photo studio, vibrant vivid color, 135mm Canon lens, Live movie, similar to Pacific Rim --no legs, person, people, man, woman, anime, cg, game --ar 21:9 --v 6',
      promptEn:
        'Overhead drone view, speed to the clouds, black and dark green blue tones and a powerful sense of oppression, Portrait of a dragon with two massive wings, hugging body tightly covering the sky, close shot, movie poster vibe, thunderstorm, at night, dynamic photography, huge whirlpool, towering tsunami, photo studio, vibrant vivid color, 135mm Canon lens, Live movie, similar to Pacific Rim --no legs, person, people, man, woman, anime, cg, game --ar 21:9 --v 6',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'Overhead drone view, speed to the clouds, black and dark green blue tones and a powerful sense of oppression, Portrait of a dragon with two massive wings, hugging body tightly covering the sky, close shot, movie poster vibe, thunderstorm, at night, dynamic photography, huge whirlpool, towering tsunami, photo studio, vibrant vivid color, 135mm Canon lens, Live movie, similar to Pacific Rim --no legs, person, people, man, woman, anime, cg, game --ar 21:9 --v 6.0 --s 250',
        flags: 0,
        messageHash: '2631fc4d-31d2-4925-9d55-02d010943bdf',
        messageId: '1196363141219618836',
        nonce: '1467911983637544960',
        notifyHook: null,
        progressMessageId: '1196362675622514718',
      },
      startTime: 1705305488386,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705305488385,
    },
    {
      action: 'IMAGINE',
      description:
        '/imagine a tall, rocky mountain covered in snow and fog, in the style of santiago rusinol, gritty reportage, juxtaposition of hard and soft lines, óscar domínguez, rough textures, low depth of field --ar 16:9 --v 6.0',
      failReason: null,
      finishTime: 1705305958903,
      id: '1705305919492541',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196364644395257886/meng1011_a_tall_rocky_mountain_covered_in_snow_and_fog_in_the_s_dd23072e-a024-48d9-a7ae-c0ef003856fd.png?ex=65b75c66&is=65a4e766&hm=cde3f4b43218ac4660e542aefe2ea401858d35a40a6cb62efefbaec70f57bee4&',
      progress: '100%',
      prompt:
        'a tall, rocky mountain covered in snow and fog, in the style of santiago rusinol, gritty reportage, juxtaposition of hard and soft lines, óscar domínguez, rough textures, low depth of field --ar 16:9 --v 6.0',
      promptEn:
        'a tall, rocky mountain covered in snow and fog, in the style of santiago rusinol, gritty reportage, juxtaposition of hard and soft lines, óscar domínguez, rough textures, low depth of field --ar 16:9 --v 6.0',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'a tall, rocky mountain covered in snow and fog, in the style of santiago rusinol, gritty reportage, juxtaposition of hard and soft lines, óscar domínguez, rough textures, low depth of field --ar 16:9 --v 6.0 --s 250',
        flags: 0,
        messageHash: 'dd23072e-a024-48d9-a7ae-c0ef003856fd',
        messageId: '1196364644835667989',
        nonce: '1467913791831359488',
        notifyHook: null,
        progressMessageId: '1196364488501362728',
      },
      startTime: 1705305919493,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705305919492,
    },
    {
      action: 'IMAGINE',
      description: '/imagine a pig',
      failReason: null,
      finishTime: 1705308864583,
      id: '1705308810603472',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196376831666966548/meng1011_a_pig_5bc771d2-c51f-47e3-81b2-f38ce533bce1.png?ex=65b767c0&is=65a4f2c0&hm=ad4a45d6567193001d599e0f5b70efad059b1f1b51084082adf816a82e957398&',
      progress: '100%',
      prompt: 'a pig',
      promptEn: 'a pig',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt: 'a pig --v 6.0 --s 250',
        flags: 0,
        messageHash: '5bc771d2-c51f-47e3-81b2-f38ce533bce1',
        messageId: '1196376831885053974',
        nonce: '1467925918029791232',
        notifyHook: null,
        progressMessageId: '1196376609402388550',
      },
      startTime: 1705308810604,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705308810603,
    },
    {
      action: 'IMAGINE',
      description: '/imagine a bird',
      failReason: null,
      finishTime: 1705310324053,
      id: '1705310285146113',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196382952741945424/meng1011_a_bird_822d0ff9-e8da-4a35-b71f-78f59ad4ee1f.png?ex=65b76d73&is=65a4f873&hm=bb30ac4aa536d22fa28d06943127ba7a955047ae8138d48f3e86ccfaa4a24975&',
      progress: '100%',
      prompt: 'a bird',
      promptEn: 'a bird',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt: 'a bird --v 6.0 --s 250',
        flags: 0,
        messageHash: '822d0ff9-e8da-4a35-b71f-78f59ad4ee1f',
        messageId: '1196382953580810310',
        nonce: '1467932102711394304',
        notifyHook: null,
        progressMessageId: '1196382794021097573',
      },
      startTime: 1705310285146,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705310285146,
    },
    {
      action: 'IMAGINE',
      description:
        '/imagine Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      failReason: null,
      finishTime: 1705310645445,
      id: '1705310583960676',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196384301101617262/meng1011_Dragon_sleeping_on_clouds_translucent_glass_zbrush_rub_4bed1f85-d7d9-4c68-b4d8-7652991e0b65.png?ex=65b76eb5&is=65a4f9b5&hm=198de4766243776970c66ba736a25a526110a051eadff6fcf46bbdeacda88d4d&',
      progress: '100%',
      prompt:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      promptEn:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
        flags: 0,
        messageHash: '4bed1f85-d7d9-4c68-b4d8-7652991e0b65',
        messageId: '1196384301479116861',
        nonce: '1467933356028149760',
        notifyHook: null,
        progressMessageId: '1196384047509815366',
      },
      startTime: 1705310583961,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705310583960,
    },
    {
      action: 'UPSCALE',
      description: '/up 1705310583960676 U2',
      failReason: null,
      finishTime: 1705322485865,
      id: '1705322474985695',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196433963552604160/meng1011_Dragon_sleeping_on_clouds_translucent_glass_zbrush_rub_8bbf1b28-62cf-46a7-b32a-48ef80733dbf.png?ex=65b79cf5&is=65a527f5&hm=622e37125a36a4e9bb2e6b48552bfe4d82c455d5a95075225cb174cff649fb9b&',
      progress: '100%',
      prompt:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      promptEn:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
        flags: 0,
        messageHash: '8bbf1b28-62cf-46a7-b32a-48ef80733dbf',
        messageId: '1196433963871387698',
        nonce: '1467983230601871360',
        notifyHook: null,
        progressMessageId: '1196433921940914216',
      },
      startTime: 1705322474987,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705322474985,
    },
    {
      action: 'UPSCALE',
      description: '/up 1705310285146113 U1',
      failReason: null,
      finishTime: 1705322518063,
      id: '1705322516641400',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196434098756005918/meng1011_a_bird_beff9fc1-1fe6-4a75-a1d6-7ff9427c09b8.png?ex=65b79d15&is=65a52815&hm=2b51773b7a1e01b477ebdfdadab181cbb0427a45fff95497c204710e9f3e4bc6&',
      progress: '100%',
      prompt: 'a bird',
      promptEn: 'a bird',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt: 'a bird --v 6.0 --s 250',
        flags: 0,
        messageHash: 'beff9fc1-1fe6-4a75-a1d6-7ff9427c09b8',
        messageId: '1196434099087343727',
        nonce: '1467983405319798784',
        notifyHook: null,
        progressMessageId: '1196434097199923220',
      },
      startTime: 1705322516641,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705322516641,
    },
    {
      action: 'UPSCALE',
      description: '/up 1705310583960676 U1',
      failReason: null,
      finishTime: 1705322781614,
      id: '1705322771285873',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1174150905801736255/1196435203825086575/meng1011_Dragon_sleeping_on_clouds_translucent_glass_zbrush_rub_9116287e-5c5a-4368-b1ca-f78afa743c73.png?ex=65b79e1d&is=65a5291d&hm=bf2079e53116dfb30265fea170cae89acbe6c6375ea37a8bc2529ed67e9d5127&',
      progress: '100%',
      prompt:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      promptEn:
        'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
      properties: {
        discordInstanceId: '1174150905801736255',
        finalPrompt:
          'Dragon sleeping on clouds, translucent glass, zbrush, ruby and gold style, anime aesthetics, furry art, red and white, elaborate, c4d rendering, super high detail, 3d --ar 9:16 --stylize 250 --v 6.0',
        flags: 0,
        messageHash: '9116287e-5c5a-4368-b1ca-f78afa743c73',
        messageId: '1196435204299051048',
        nonce: '1467984473374146560',
        notifyHook: null,
        progressMessageId: '1196435165283614790',
      },
      startTime: 1705322771286,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705322771285,
    },
    {
      action: 'VARIATION',
      description: '/up 1705543971814470 V4',
      failReason: null,
      finishTime: 1705544090932,
      id: '1705544036857582',
      imageUrl:
        'https://cdn.discordapp.com/attachments/1196999262974787594/1197363439547449486/caicaizhiwen_a_dog_d8ac47e5-7cd8-44f0-a92b-1224b4d3e465.png?ex=65bafe99&is=65a88999&hm=38c6c7887978982857d306045fd1aacbc0348186679941b78e10d3fa5b64f2fd&',
      progress: '100%',
      prompt: 'a dog',
      promptEn: 'a dog',
      properties: {
        discordInstanceId: '1196999262974787594',
        finalPrompt: 'a dog',
        flags: 0,
        messageHash: 'd8ac47e5-7cd8-44f0-a92b-1224b4d3e465',
        messageId: '1197363439786545205',
        nonce: '1468912528447848448',
        notifyHook: null,
        progressMessageId: '1197363219027730534',
      },
      startTime: 1705544036858,
      state: null,
      status: 'SUCCESS',
      submitTime: 1705544036857,
    },
  ],
};
