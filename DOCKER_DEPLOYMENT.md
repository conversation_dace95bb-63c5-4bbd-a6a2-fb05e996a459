# Docker 部署指南

本指南将帮助你使用Docker部署Lobe Midjourney WebUI项目。

## 前置要求

1. 安装Docker和Docker Compose
2. 确保你有一个可用的Midjourney代理服务（参考[midjourney-proxy部署文档](https://github.com/novicezk/midjourney-proxy/)）

## 快速开始

### 方法1：使用一键启动脚本（推荐）

1. 克隆项目并进入目录：
```bash
git clone https://github.com/lobehub/lobe-midjourney-webui.git
cd lobe-midjourney-webui
```

2. 运行启动脚本：
```bash
./start.sh
```

3. 访问应用：
打开浏览器访问 `http://localhost:3000`

### 方法2：使用Docker Compose

1. 克隆项目并进入目录：
```bash
git clone https://github.com/lobehub/lobe-midjourney-webui.git
cd lobe-midjourney-webui
```

2. 构建并启动服务：
```bash
docker compose up -d --build
```

3. 访问应用：
打开浏览器访问 `http://localhost:3000`

### 方法3：使用Docker命令

1. 构建镜像：
```bash
docker build -t lobe-midjourney-webui .
```

2. 运行容器：
```bash
docker run -d \
  --name lobe-midjourney-webui \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e NEXT_TELEMETRY_DISABLED=1 \
  lobe-midjourney-webui
```

## 环境变量配置

你可以通过环境变量来配置应用：

- `NODE_ENV`: 设置为 `production`
- `NEXT_TELEMETRY_DISABLED`: 设置为 `1` 禁用Next.js遥测
- `MIDJOURNEY_PROXY_URL`: 你的Midjourney代理服务URL

### 在docker-compose.yml中配置环境变量：

```yaml
environment:
  - NODE_ENV=production
  - NEXT_TELEMETRY_DISABLED=1
  - MIDJOURNEY_PROXY_URL=http://your-midjourney-proxy:8080
```

## 端口配置

默认情况下，应用运行在3000端口。如果你想使用其他端口，可以修改docker-compose.yml：

```yaml
ports:
  - "8080:3000"  # 将本地8080端口映射到容器3000端口
```

## 数据持久化

如果你的应用需要持久化数据，可以在docker-compose.yml中添加volumes：

```yaml
volumes:
  - ./data:/app/data
```

## 日志查看

查看应用日志：
```bash
docker compose logs -f lobe-midjourney-webui
```

## 停止和重启

停止服务：
```bash
./stop.sh
# 或者
docker compose down
```

重启服务：
```bash
docker compose restart
```

## 更新应用

1. 拉取最新代码：
```bash
git pull origin main
```

2. 重新构建并启动：
```bash
docker compose up -d --build
```

## 故障排除

### 常见问题

1. **端口冲突**：如果3000端口被占用，修改docker-compose.yml中的端口映射

2. **构建失败**：确保你有足够的磁盘空间和网络连接

3. **应用无法访问**：检查防火墙设置和端口映射

### 查看容器状态

```bash
docker compose ps
```

### 进入容器调试

```bash
docker compose exec lobe-midjourney-webui sh
```

## 生产环境建议

1. 使用反向代理（如Nginx）
2. 配置SSL证书
3. 设置适当的资源限制
4. 配置日志轮转
5. 定期备份数据

## 安全注意事项

1. 不要在生产环境中暴露不必要的端口
2. 使用强密码和安全的环境变量
3. 定期更新Docker镜像
4. 配置适当的网络安全组
