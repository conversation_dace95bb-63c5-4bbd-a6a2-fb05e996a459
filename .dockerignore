# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 本地环境变量文件
.env*.local

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# 开发工具
.vscode
.idea

# 操作系统
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# 文档
README.md
README.zh-CN.md
CHANGELOG.md
docs/

# 测试
coverage/
.nyc_output

# 构建输出
.next/
out/
dist/

# 日志
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 临时文件
.tmp
.temp

# 其他
.eslintcache
.stylelintcache
