<a name="readme-top"></a>

# Changelog

## [Version 1.5.0](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.8...v1.5.0)

<sup>Released on **2024-06-25**</sup>

#### ✨ Features

- **misc**: Support midjourney-proxy API secret.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support midjourney-proxy API secret, closes [#29](https://github.com/lobehub/lobe-midjourney-webui/issues/29) ([ba6668c](https://github.com/lobehub/lobe-midjourney-webui/commit/ba6668c))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.8](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.7...v1.4.8)

<sup>Released on **2024-05-27**</sup>

#### 🐛 Bug Fixes

- **misc**: Upgrade deps and fix build.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Upgrade deps and fix build ([17fbe00](https://github.com/lobehub/lobe-midjourney-webui/commit/17fbe00))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.7](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.6...v1.4.7)

<sup>Released on **2024-03-16**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix init of chat plugin.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix init of chat plugin ([cfddedd](https://github.com/lobehub/lobe-midjourney-webui/commit/cfddedd))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.6](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.5...v1.4.6)

<sup>Released on **2024-02-14**</sup>

#### 💄 Styles

- **misc**: Update base experience.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update base experience ([c88913d](https://github.com/lobehub/lobe-midjourney-webui/commit/c88913d))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.5](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.4...v1.4.5)

<sup>Released on **2024-01-22**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix broken links in READMEs.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix broken links in READMEs ([4bba684](https://github.com/lobehub/lobe-midjourney-webui/commit/4bba684))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.4](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.3...v1.4.4)

<sup>Released on **2024-01-21**</sup>

#### 💄 Styles

- **misc**: Improve loading and social media.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve loading and social media ([c2d8e6f](https://github.com/lobehub/lobe-midjourney-webui/commit/c2d8e6f))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.3](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.2...v1.4.3)

<sup>Released on **2024-01-21**</sup>

#### 💄 Styles

- **misc**: Improve loading.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve loading ([11d9b37](https://github.com/lobehub/lobe-midjourney-webui/commit/11d9b37))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.2](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.1...v1.4.2)

<sup>Released on **2024-01-20**</sup>

#### 💄 Styles

- **misc**: Improve loading.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve loading ([0bd5002](https://github.com/lobehub/lobe-midjourney-webui/commit/0bd5002))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.4.1](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.4.0...v1.4.1)

<sup>Released on **2024-01-20**</sup>

#### 💄 Styles

- **misc**: Improve loading, improve loading.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve loading ([c15b469](https://github.com/lobehub/lobe-midjourney-webui/commit/c15b469))
- **misc**: Improve loading ([2aedb73](https://github.com/lobehub/lobe-midjourney-webui/commit/2aedb73))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 1.4.0](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.3.3...v1.4.0)

<sup>Released on **2024-01-20**</sup>

#### ✨ Features

- **misc**: Support image upload feature.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support image upload feature, closes [#9](https://github.com/lobehub/lobe-midjourney-webui/issues/9) ([d3e2fe4](https://github.com/lobehub/lobe-midjourney-webui/commit/d3e2fe4))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.3.3](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.3.2...v1.3.3)

<sup>Released on **2024-01-20**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with new folder.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with new folder ([73b78e5](https://github.com/lobehub/lobe-midjourney-webui/commit/73b78e5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.3.2](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.3.1...v1.3.2)

<sup>Released on **2024-01-20**</sup>

#### 🐛 Bug Fixes

- **misc**: Fix discord url.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Fix discord url ([8cc70f1](https://github.com/lobehub/lobe-midjourney-webui/commit/8cc70f1))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.3.1](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.3.0...v1.3.1)

<sup>Released on **2024-01-20**</sup>

#### 💄 Styles

- **misc**: Improve open graph.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve open graph ([7f082b2](https://github.com/lobehub/lobe-midjourney-webui/commit/7f082b2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 1.3.0](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.11...v1.3.0)

<sup>Released on **2024-01-20**</sup>

#### ✨ Features

- **misc**: Support locale and fix settings.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support locale and fix settings, closes [#6](https://github.com/lobehub/lobe-midjourney-webui/issues/6) ([11857ea](https://github.com/lobehub/lobe-midjourney-webui/commit/11857ea))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.11](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.10...v1.2.11)

<sup>Released on **2024-01-19**</sup>

#### 💄 Styles

- **misc**: Fix some style problem.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix some style problem ([b334d5a](https://github.com/lobehub/lobe-midjourney-webui/commit/b334d5a))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.10](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.9...v1.2.10)

<sup>Released on **2024-01-19**</sup>

#### 💄 Styles

- **misc**: Update mobile usage.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Update mobile usage ([d0f9188](https://github.com/lobehub/lobe-midjourney-webui/commit/d0f9188))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.9](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.8...v1.2.9)

<sup>Released on **2024-01-19**</sup>

#### 💄 Styles

- **misc**: Fix mobile usage.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Fix mobile usage ([bf67c55](https://github.com/lobehub/lobe-midjourney-webui/commit/bf67c55))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.8](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.7...v1.2.8)

<sup>Released on **2024-01-19**</sup>

#### 💄 Styles

- **misc**: Improve basic user experience.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve basic user experience, closes [#5](https://github.com/lobehub/lobe-midjourney-webui/issues/5) ([6d73832](https://github.com/lobehub/lobe-midjourney-webui/commit/6d73832))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.7](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.6...v1.2.7)

<sup>Released on **2024-01-19**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor with mj store.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor with mj store ([4d7e9d3](https://github.com/lobehub/lobe-midjourney-webui/commit/4d7e9d3))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.6](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.5...v1.2.6)

<sup>Released on **2024-01-19**</sup>

#### 💄 Styles

- **misc**: Close drawer on press save.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Close drawer on press save ([91d65fe](https://github.com/lobehub/lobe-midjourney-webui/commit/91d65fe))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.5](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.4...v1.2.5)

<sup>Released on **2024-01-18**</sup>

#### 💄 Styles

- **misc**: Improve style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: Improve style ([1342cd2](https://github.com/lobehub/lobe-midjourney-webui/commit/1342cd2))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.4](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.3...v1.2.4)

<sup>Released on **2024-01-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve plugin ux.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve plugin ux ([b5136ca](https://github.com/lobehub/lobe-midjourney-webui/commit/b5136ca))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.3](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.2...v1.2.3)

<sup>Released on **2024-01-18**</sup>

#### 🐛 Bug Fixes

- **misc**: Improve plugin ux.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Improve plugin ux ([7a7b97b](https://github.com/lobehub/lobe-midjourney-webui/commit/7a7b97b))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.2](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.1...v1.2.2)

<sup>Released on **2024-01-18**</sup>

#### 💄 Styles

- **misc**: 补充说明引导.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Styles

- **misc**: 补充说明引导 ([88606a8](https://github.com/lobehub/lobe-midjourney-webui/commit/88606a8))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.2.1](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.2.0...v1.2.1)

<sup>Released on **2024-01-18**</sup>

#### 🐛 Bug Fixes

- **misc**: 修正没有将设置缓存下来的问题.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: 修正没有将设置缓存下来的问题 ([f708fc5](https://github.com/lobehub/lobe-midjourney-webui/commit/f708fc5))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 1.2.0](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.1.0...v1.2.0)

<sup>Released on **2024-01-18**</sup>

#### ✨ Features

- **misc**: Support save onLocal.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Support save onLocal ([fa191dd](https://github.com/lobehub/lobe-midjourney-webui/commit/fa191dd))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## [Version 1.1.0](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.0.1...v1.1.0)

<sup>Released on **2024-01-17**</sup>

#### ✨ Features

- **misc**: Update prod manifest, 优化兼容 LobeChat 插件内逻辑.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's improved

- **misc**: Update prod manifest ([89a08e8](https://github.com/lobehub/lobe-midjourney-webui/commit/89a08e8))
- **misc**: 优化兼容 LobeChat 插件内逻辑 ([16b3a80](https://github.com/lobehub/lobe-midjourney-webui/commit/16b3a80))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

### [Version 1.0.1](https://github.com/lobehub/lobe-midjourney-webui/compare/v1.0.0...v1.0.1)

<sup>Released on **2024-01-15**</sup>

#### 🐛 Bug Fixes

- **misc**: Better error handle.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### What's fixed

- **misc**: Better error handle ([e3d0c26](https://github.com/lobehub/lobe-midjourney-webui/commit/e3d0c26))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>

## Version 1.0.0

<sup>Released on **2024-01-15**</sup>

#### ♻ Code Refactoring

- **misc**: Refactor the api implement, refactor with tasks.

#### ✨ Features

- **misc**: Finish all process, support basic feature.

#### 💄 Styles

- **misc**: Improve image preview style.

<br/>

<details>
<summary><kbd>Improvements and Fixes</kbd></summary>

#### Code refactoring

- **misc**: Refactor the api implement ([f43fdf5](https://github.com/lobehub/lobe-midjourney-webui/commit/f43fdf5))
- **misc**: Refactor with tasks ([bb93628](https://github.com/lobehub/lobe-midjourney-webui/commit/bb93628))

#### What's improved

- **misc**: Finish all process ([f7f8d2f](https://github.com/lobehub/lobe-midjourney-webui/commit/f7f8d2f))
- **misc**: Support basic feature ([00b36f4](https://github.com/lobehub/lobe-midjourney-webui/commit/00b36f4))

#### Styles

- **misc**: Improve image preview style ([df42a62](https://github.com/lobehub/lobe-midjourney-webui/commit/df42a62))

</details>

<div align="right">

[![](https://img.shields.io/badge/-BACK_TO_TOP-151515?style=flat-square)](#readme-top)

</div>
